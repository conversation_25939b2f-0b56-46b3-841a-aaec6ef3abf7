import { defineStore } from 'pinia'

export const useEvidenceItemsStore = defineStore('evidenceItems', {
  state: () => ({
    selectedItem: null,
    items: [],
    // 所有表单字段统一放在formData中
    formData: {
      title: '',
      source: '',
      isZhanjiangDeployment: '是',
      enforcementAgency: '',
      caseTime: '',
      address: '',
      detailedAddress: '',
      jointEnforcementAgency: '',
      caseReason: '',
      selectedCaseReasons: [],
      transportDetails: '',
      partyInvolved: '',
      licenseNo: ''
    }
  }),
  actions: {
    setSelectedItem(item) {
      this.selectedItem = item
    },
    clearSelectedItem() {
      this.selectedItem = null
    },
    addItem(item) {
      this.items.unshift(item) // Add new items to the beginning of the array
    },
    updateItem(index, item) {
      this.items[index] = item
    },
    removeItem(index) {
      this.items.splice(index, 1)
    },
    clearItems() {
      this.items = []
    },
    setItems(items) {
      this.items = items
    },
    // 保存表单数据
    setFormData(formData) {
      this.formData = {
        title: formData.title || '',
        source: formData.source || '',
        isZhanjiangDeployment: formData.isZhanjiangDeployment || '是',
        enforcementAgency: formData.enforcementAgency || '',
        caseTime: formData.caseTime || '',
        address: formData.address || '',
        detailedAddress: formData.detailedAddress || '',
        jointEnforcementAgency: formData.jointEnforcementAgency || '',
        caseReason: formData.caseReason || '',
        selectedCaseReasons: formData.selectedCaseReasons || [],
        transportDetails: formData.transportDetails || '',
        partyInvolved: formData.partyInvolved || '',
        licenseNo: formData.licenseNo || ''
      }
    },
    // 清除表单数据
    clearFormData() {
      this.formData = {
        title: '',
        source: '',
        isZhanjiangDeployment: '是',
        enforcementAgency: '',
        caseTime: '',
        address: '',
        detailedAddress: '',
        jointEnforcementAgency: '',
        caseReason: '',
        selectedCaseReasons: [],
        transportDetails: '',
        partyInvolved: '',
        licenseNo: ''
      }
    }
  }
})