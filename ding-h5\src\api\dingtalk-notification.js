import { http } from '@/utils/http'

/**
 * 获取所有部门用户列表
 * 管理员专用接口，用于测试
 * @returns {Promise<Object>} 所有部门用户列表
 */
export function getAllDepartmentUsers() {
  return http.get('/api/dingapp/notification/users/all')
}

/**
 * 获取目标部门及其所有下级部门的用户信息（前端传参）
 * @param {Array<string>} deptIds 部门ID列表
 * @returns {Promise<Object>} 目标部门及下级部门用户信息
 */
export function getTargetDepartmentsWithSubUsersDetails(deptIds) {
  return http.post('/api/dingapp/notification/users/target-with-sub', deptIds)
}
